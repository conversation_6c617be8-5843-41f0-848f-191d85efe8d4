# -*- coding: utf-8 -*-
"""
共享画板系统常量定义
Shared Drawing Protocol Protocol (SDPP) Constants
"""

# 协议版本
PROTOCOL_VERSION = "1.0"

# 服务器配置
DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 8888
MAX_CONNECTIONS = 50
BUFFER_SIZE = 4096

# 消息类型
class MessageType:
    # 认证相关
    AUTH_REQUEST = "auth_request"
    AUTH_RESPONSE = "auth_response"
    LOGOUT = "logout"
    
    # 绘画相关
    DRAW_START = "draw_start"      # 开始绘画
    DRAW_MOVE = "draw_move"        # 绘画移动
    DRAW_END = "draw_end"          # 结束绘画
    CLEAR_BOARD = "clear_board"    # 清空画板
    
    # 同步相关
    SYNC_REQUEST = "sync_request"  # 请求同步
    SYNC_RESPONSE = "sync_response" # 同步响应
    BROADCAST = "broadcast"        # 广播消息
    
    # 用户管理
    USER_LIST = "user_list"        # 用户列表
    USER_JOIN = "user_join"        # 用户加入
    USER_LEAVE = "user_leave"      # 用户离开
    
    # 系统相关
    HEARTBEAT = "heartbeat"        # 心跳
    ERROR = "error"                # 错误消息
    SUCCESS = "success"            # 成功消息

# 错误码
class ErrorCode:
    SUCCESS = 0
    INVALID_MESSAGE = 1001
    AUTH_FAILED = 1002
    USER_EXISTS = 1003
    USER_NOT_FOUND = 1004
    PERMISSION_DENIED = 1005
    INVALID_COMMAND = 1006
    SERVER_ERROR = 1007
    CONNECTION_LOST = 1008
    PROTOCOL_VERSION_MISMATCH = 1009

# 错误消息
ERROR_MESSAGES = {
    ErrorCode.SUCCESS: "操作成功",
    ErrorCode.INVALID_MESSAGE: "无效的消息格式",
    ErrorCode.AUTH_FAILED: "认证失败",
    ErrorCode.USER_EXISTS: "用户已存在",
    ErrorCode.USER_NOT_FOUND: "用户不存在",
    ErrorCode.PERMISSION_DENIED: "权限不足",
    ErrorCode.INVALID_COMMAND: "无效的命令",
    ErrorCode.SERVER_ERROR: "服务器内部错误",
    ErrorCode.CONNECTION_LOST: "连接丢失",
    ErrorCode.PROTOCOL_VERSION_MISMATCH: "协议版本不匹配"
}

# 绘画相关常量
class DrawingConstants:
    MIN_BRUSH_SIZE = 1
    MAX_BRUSH_SIZE = 20
    DEFAULT_BRUSH_SIZE = 3
    DEFAULT_COLOR = "#000000"
    
    # 画板尺寸
    BOARD_WIDTH = 800
    BOARD_HEIGHT = 600

# 用户权限
class UserRole:
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

# 网络配置
HEARTBEAT_INTERVAL = 30  # 心跳间隔（秒）
CONNECTION_TIMEOUT = 60  # 连接超时（秒）
MAX_MESSAGE_SIZE = 1024 * 1024  # 最大消息大小（1MB）

# 安全配置
SALT_LENGTH = 16
HASH_ITERATIONS = 100000
